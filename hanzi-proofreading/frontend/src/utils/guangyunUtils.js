/**
 * 广韵数据处理工具类
 */

// 来源名称映射
export const SOURCE_NAMES = {
  'xxt': '小学堂',
  'qx': '全息',
  'yd': '韵典'
}

// 字段标签映射
export const FIELD_LABELS = {
  hanzi: '汉字',
  unicode: '编码',
  fan_qie: '反切',
  kai_he: '开合',
  sheng_diao: '声调',
  sheng_mu: '声母',
  yun_bu: '韵部',
  deng_di: '等第',
  xiao_yun: '小韵',
  qing_zhuo: '清浊',
  she: '摄',
  shi_yi: '释义'
}

// 音韵字段配置
export const PHONETIC_FIELDS = [
  { key: 'fan_qie', label: '反切' },
  { key: 'sheng_mu', label: '声母' },
  { key: 'yun_bu', label: '韵部' },
  { key: 'sheng_diao', label: '声调' },
  { key: 'kai_he', label: '开合' },
  { key: 'deng_di', label: '等第' },
  { key: 'xiao_yun', label: '小韵' },
  { key: 'qing_zhuo', label: '清浊' },
  { key: 'she', label: '摄' }
]

// 来源数据字段配置
export const SOURCE_FIELDS = [
  { key: 'hanzi', label: '汉字' },
  { key: 'unicode', label: '编码' },
  { key: 'fan_qie', label: '反切' },
  { key: 'kai_he', label: '开合' },
  { key: 'she', label: '摄' },
  { key: 'sheng_diao', label: '声调' },
  { key: 'xiao_yun', label: '小韵' },
  { key: 'sheng_mu', label: '声母' },
  { key: 'qing_zhuo', label: '清浊' },
  { key: 'yun_bu', label: '韵部' },
  { key: 'deng_di', label: '等第' }
]

/**
 * 获取来源名称
 * @param {string} source 来源代码
 * @returns {string} 来源名称
 */
export function getSourceName(source) {
  return SOURCE_NAMES[source] || source
}

/**
 * 获取字段标签
 * @param {string} fieldName 字段名
 * @returns {string} 字段标签
 */
export function getFieldLabel(fieldName) {
  return FIELD_LABELS[fieldName] || fieldName
}

/**
 * 标准化反切值
 * @param {string} fanQie 反切值
 * @param {string} source 来源（可选）
 * @returns {string|null} 标准化后的反切值
 */
export function normalizeFanQie(fanQie, source = null) {
  if (!fanQie || fanQie === '-' || fanQie.trim() === '') {
    return null
  }

  let normalized = fanQie.trim()

  // 只有韵典来源且不以"切"结尾时才添加"切"
  if (source === 'yd' && !normalized.endsWith('切')) {
    normalized = normalized + '切'
  }

  return normalized
}

/**
 * 检查是否为中文字符
 * @param {string} char 字符
 * @returns {boolean} 是否为中文字符
 */
export function isChineseCharacter(char) {
  if (!char || char.length === 0) return false

  const codePoint = char.codePointAt(0)

  // 基本汉字范围
  if (codePoint >= 0x4E00 && codePoint <= 0x9FFF) return true
  // 扩展A区
  if (codePoint >= 0x3400 && codePoint <= 0x4DBF) return true
  // 扩展B区
  if (codePoint >= 0x20000 && codePoint <= 0x2A6DF) return true
  // 扩展C区
  if (codePoint >= 0x2A700 && codePoint <= 0x2B73F) return true
  // 扩展D区
  if (codePoint >= 0x2B740 && codePoint <= 0x2B81F) return true
  // 扩展E区
  if (codePoint >= 0x2B820 && codePoint <= 0x2CEAF) return true
  // 扩展F区
  if (codePoint >= 0x2CEB0 && codePoint <= 0x2EBEF) return true

  return false
}

/**
 * 解析用户输入的汉字或Unicode编码
 * @param {string} input 用户输入
 * @returns {Object|null} 解析结果
 */
export function parseHanziInput(input) {
  if (!input) return null

  // 情况1: 直接输入汉字
  if (input.length >= 1 && isChineseCharacter(input)) {
    const codePoint = input.codePointAt(0)
    const unicodeCode = codePoint.toString(16).toUpperCase()
    return {
      character: input,
      unicode_code: unicodeCode,
      unicode: `U+${unicodeCode}`
    }
  }

  // 情况2: 输入Unicode编码
  let unicodeCode = ''

  // 处理各种Unicode编码格式
  if (input.startsWith('U+') || input.startsWith('u+')) {
    unicodeCode = input.substring(2).toUpperCase()
  } else if (input.startsWith('0x') || input.startsWith('0X')) {
    unicodeCode = input.substring(2).toUpperCase()
  } else if (/^[0-9A-Fa-f]+$/.test(input)) {
    unicodeCode = input.toUpperCase()
  } else {
    return null
  }

  // 验证Unicode编码格式
  if (!/^[0-9A-F]+$/.test(unicodeCode)) {
    return null
  }

  // 转换为字符
  try {
    const codePoint = parseInt(unicodeCode, 16)
    const character = String.fromCodePoint(codePoint)

    // 验证是否为有效的中文字符
    if (!isChineseCharacter(character)) {
      return null
    }

    return {
      character: character,
      unicode_code: unicodeCode,
      unicode: `U+${unicodeCode}`
    }
  } catch (error) {
    return null
  }
}

/**
 * 生成汉字徽章
 * @param {Object} metadata 汉字元数据
 * @returns {Array} 徽章数组
 */
export function generateBadges(metadata) {
  const badges = []

  if (metadata.is_fan_ti_zi) {
    badges.push({
      type: 'traditional',
      class: 'badge-traditional',
      text: '繁',
      title: '繁体字'
    })
  }

  if (metadata.is_jian_ti_zi) {
    badges.push({
      type: 'simplified',
      class: 'badge-simplified',
      text: '简',
      title: '简体字'
    })
  }

  if (metadata.is_zheng_ti_zi) {
    badges.push({
      type: 'standard',
      class: 'badge-standard',
      text: '正',
      title: '正体字'
    })
  }

  if (metadata.is_yi_ti_zi) {
    badges.push({
      type: 'variant',
      class: 'badge-variant',
      text: '异',
      title: '异体字'
    })
  }

  return badges
}

/**
 * 格式化当前时间
 * @returns {string} 格式化的时间字符串
 */
export function formatCurrentTime() {
  return new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 获取字段的CSS类，用于标红不一致的字段
 * @param {string} fieldName 字段名
 * @param {string} fieldValue 字段值
 * @param {string} hanzi 汉字
 * @param {Object} currentSource 当前来源数据
 * @param {Array} sourceData 所有来源数据
 * @param {Object} pronunciation 校对数据（可选）
 * @returns {string} CSS类名
 */
export function getFieldClass(fieldName, fieldValue, hanzi, currentSource, sourceData, pronunciation = null) {
  // 如果没有足够的数据进行比较，返回空
  if (!hanzi || !sourceData || sourceData.length < 2 || !currentSource) {
    return ''
  }

  // 获取同一个汉字的所有来源数据
  const sameHanziData = sourceData.filter(source => source.hanzi === hanzi)

  // 如果只有一个来源，不需要比较
  if (sameHanziData.length < 2) {
    return ''
  }

  let compareData = []

  // 如果有校对数据且有ID，优先按ref关联进行比较
  if (pronunciation && pronunciation.id) {
    compareData = sameHanziData.filter(source => source.ref === pronunciation.id)
  }

  // 如果没有ref关联的数据或没有校对数据，则按反切值进行比较
  if (compareData.length < 2) {
    // 获取当前数据项的反切值（进行标准化处理）
    let currentFanQie = normalizeFanQie(currentSource.fan_qie, currentSource.source)

    // 只与具有相同反切值的数据进行对比
    compareData = sameHanziData.filter(source => {
      let sourceFanQie = normalizeFanQie(source.fan_qie, source.source)
      return sourceFanQie === currentFanQie
    })
  }

  // 如果只有一个数据，不需要比较
  if (compareData.length < 2) {
    return ''
  }

  // 获取比较数据的该字段值，过滤掉空值，并进行预处理
  const fieldValues = compareData
    .map(source => {
      let value = source[fieldName]

      // 对反切字段进行特殊处理
      if (fieldName === 'fan_qie' && value && value !== '-' && value.trim() !== '') {
        value = normalizeFanQie(value, source.source)
      }

      return value
    })
    .filter(value => value && value !== '-' && value.trim() !== '')

  // 如果没有有效值或只有一个有效值，不需要标红
  if (fieldValues.length <= 1) {
    return ''
  }

  // 检查是否所有有效值都相同
  const uniqueValues = [...new Set(fieldValues)]

  // 如果有多个不同的值，标红
  if (uniqueValues.length > 1) {
    return 'field-conflict'
  }

  return ''
}
